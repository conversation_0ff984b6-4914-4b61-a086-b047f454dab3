#usda 1.0
(  # 全局属性
    customLayerData = {
        dictionary cameraSettings = {
            dictionary Front = {
                double3 position = (5, 0, 0)
                double radius = 5
            }
            dictionary Perspective = {
                double3 position = (-0.5220952693483991, 0.12999142353431076, 0.2963320617586902)
                double3 target = (0.19416083248877092, -0.12496313889129312, -0.08972909636609344)
            }
            dictionary Right = {
                double3 position = (0, -5, 0)
                double radius = 5
            }
            dictionary Top = {
                double3 position = (0, 0, 5)
                double radius = 5
            }
            string boundCamera = "/OmniverseKit_Persp"
        }
        dictionary omni_layer = {
            dictionary locked = {
            }
            dictionary muteness = {
            }
        }
        dictionary renderSettings = {
        }
    }
    defaultPrim = "World" # 根prim
    endTimeCode = 1000000
    metersPerUnit = 1
    startTimeCode = 0
    timeCodesPerSecond = 60
    upAxis = "Z"
)

def Xform "World"
{
    def Xform "default_environment" (
        prepend references = @../../../../isaacsim_assets/Assets/Isaac/5.0/Isaac/Environments/Grid/default_environment.usd@
    )
    {
        over "Environment"
        {
            over "Geometry"
            {
                quatd xformOp:orient = (1, 0, 0, 0)
                double3 xformOp:scale = (100, 100, 1)
                double3 xformOp:translate = (-0.425, 0.425, 0)
                uniform token[] xformOpOrder = ["xformOp:translate", "xformOp:orient", "xformOp:scale"]
            }
        }
    }

    over "onshape" (
        prepend references = @../../../../../LEAP_Hand_Sim/assets/leap_hand/robot/robot.usd@
    )
    {
        quatd xformOp:orient = (1, 0, 0, 0)
        double3 xformOp:scale = (1, 1, 1)
        double3 xformOp:translate = (0, 0, 0.09496090251819082)
        uniform token[] xformOpOrder = ["xformOp:translate", "xformOp:orient", "xformOp:scale"]

        over "palm_lower"
        {
        }

        over "mcp_joint"
        {
        }

        over "pip"
        {
        }

        over "dip"
        {
        }

        over "fingertip"
        {
        }

        over "pip_4"
        {
        }

        over "thumb_pip"
        {
        }

        over "thumb_dip"
        {
        }

        over "thumb_fingertip"
        {
        }

        over "mcp_joint_2"
        {
        }

        over "pip_2"
        {
        }

        over "dip_2"
        {
        }

        over "fingertip_2"
        {
        }

        over "mcp_joint_3"
        {
        }

        over "pip_3"
        {
        }

        over "dip_3"
        {
        }

        over "fingertip_3"
        {
        }

        over "joints"
        {
            over "a_1"
            {
                float state:angular:physics:position = 0
                float state:angular:physics:velocity = 0
            }

            over "a_12"
            {
                float state:angular:physics:position = 0
                float state:angular:physics:velocity = 0
            }

            over "a_5"
            {
                float state:angular:physics:position = 0
                float state:angular:physics:velocity = 0
            }

            over "a_9"
            {
                float state:angular:physics:position = 0
                float state:angular:physics:velocity = 0
            }

            over "a_0"
            {
                float state:angular:physics:position = 0
                float state:angular:physics:velocity = 0
            }

            over "a_13"
            {
                float state:angular:physics:position = 0
                float state:angular:physics:velocity = 0
            }

            over "a_4"
            {
                float state:angular:physics:position = 0
                float state:angular:physics:velocity = 0
            }

            over "a_8"
            {
                float state:angular:physics:position = 0
                float state:angular:physics:velocity = 0
            }

            over "a_2"
            {
                float state:angular:physics:position = 0
                float state:angular:physics:velocity = 0
            }

            over "a_14"
            {
                float state:angular:physics:position = 0
                float state:angular:physics:velocity = 0
            }

            over "a_6"
            {
                float state:angular:physics:position = 0
                float state:angular:physics:velocity = 0
            }

            over "a_10"
            {
                float state:angular:physics:position = 0
                float state:angular:physics:velocity = 0
            }

            over "a_3"
            {
                float state:angular:physics:position = 0
                float state:angular:physics:velocity = 0
            }

            over "a_15"
            {
                float state:angular:physics:position = 0
                float state:angular:physics:velocity = 0
            }

            over "a_7"
            {
                float state:angular:physics:position = 0
                float state:angular:physics:velocity = 0
            }

            over "a_11"
            {
                float state:angular:physics:position = 0
                float state:angular:physics:velocity = 0
            }
        }
    }

    over "object" (
        prepend references = @../../../../../LEAP_Hand_Sim/assets/cube/cube.usd@
    )
    {
        quatd xformOp:orient = (1, 0, 0, 0)
        double3 xformOp:scale = (1, 1, 1)
        double3 xformOp:translate = (-0.05231851204145326, -0.02809085328999379, 0.14797322876597277) # 物体位置
        uniform token[] xformOpOrder = ["xformOp:translate", "xformOp:orient", "xformOp:scale"]

        over "object"
        {
        }

        over "root_joint" (
            active = false
        )
        {
        }
    }
}

def Xform "Environment"
{ 
    quatd xformOp:orient = (1, 0, 0, 0)
    double3 xformOp:scale = (1, 1, 1)
    double3 xformOp:translate = (0, 0, 0)
    uniform token[] xformOpOrder = ["xformOp:translate", "xformOp:orient", "xformOp:scale"]

    def DistantLight "defaultLight" (
        prepend apiSchemas = ["ShapingAPI"]
    )
    {
        float inputs:angle = 1
        float inputs:intensity = 3000
        float inputs:shaping:cone:angle = 180
        float inputs:shaping:cone:softness
        float inputs:shaping:focus
        color3f inputs:shaping:focusTint
        asset inputs:shaping:ies:file
        quatd xformOp:orient = (0.6532814824381883, 0.2705980500730985, 0.27059805007309845, 0.6532814824381882)
        double3 xformOp:scale = (1, 1, 1)
        double3 xformOp:translate = (0, 0, 0)
        uniform token[] xformOpOrder = ["xformOp:translate", "xformOp:orient", "xformOp:scale"]
    }
}

def PhysicsScene "physicsScene" (
    prepend apiSchemas = ["PhysxSceneAPI"]
)
{
}